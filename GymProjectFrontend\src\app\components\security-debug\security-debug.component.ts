import { Component, OnInit } from '@angular/core';
import { BrowserSecurityService } from '../../services/browser-security.service';
import { AuthService } from '../../services/auth.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-security-debug',
  templateUrl: './security-debug.component.html',
  styleUrls: ['./security-debug.component.css'],
  standalone: false
})
export class SecurityDebugComponent implements OnInit {
  securityStats: any = {};
  isProduction: boolean = environment.production;
  currentUser: any = null;

  constructor(
    private browserSecurityService: BrowserSecurityService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.updateStats();
    
    // Auth state değişikliklerini dinle
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.updateStats();
    });
  }

  updateStats(): void {
    this.securityStats = this.browserSecurityService.getSecurityStats();
    this.currentUser = this.authService.getCurrentUser();
  }

  testSecurity(): void {
    this.browserSecurityService.testSecurity();
  }

  toggleSecurity(): void {
    if (this.securityStats.isActive) {
      this.browserSecurityService.disableSecurity();
    } else {
      this.browserSecurityService.initializeSecurity();
    }
    this.updateStats();
  }

  simulateF12(): void {
    // F12 tuşuna basılmış gibi event oluştur
    const event = new KeyboardEvent('keydown', {
      key: 'F12',
      code: 'F12',
      keyCode: 123,
      which: 123,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(event);
  }

  simulateCtrlShiftI(): void {
    // Ctrl+Shift+I kombinasyonu
    const event = new KeyboardEvent('keydown', {
      key: 'I',
      code: 'KeyI',
      ctrlKey: true,
      shiftKey: true,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(event);
  }

  simulateRightClick(): void {
    // Sağ tık event'i
    const event = new MouseEvent('contextmenu', {
      bubbles: true,
      cancelable: true,
      button: 2
    });
    document.dispatchEvent(event);
  }

  testConsole(): void {
    console.log('Bu mesaj production\'da görünmemeli');
    console.warn('Bu uyarı production\'da görünmemeli');
    console.error('Bu hata production\'da görünmemeli');
  }
}
