<!-- Loading Overlay -->
<div class="app-initializing" *ngIf="isInitializing">
  <div class="initializing-spinner">
    <div class="spinner-circle"></div>
    <div class="dumbbell">
      <div class="weight left">
        <div class="inner-weight"></div>
      </div>
      <div class="handle"></div>
      <div class="weight right">
        <div class="inner-weight"></div>
      </div>
    </div>
  </div>
</div>

<!-- Main App Container -->
<div class="app-container" [ngClass]="{'sidebar-collapsed': sidebarCollapsed, 'initializing': isInitializing}">
  <!-- Sidebar -->
<app-sidebar *ngIf="showNavbar"
  [collapsed]="sidebarCollapsed"
  [isDarkMode]="isDarkMode"
  (toggleSidebar)="toggleSidebar()"
  (toggleDarkMode)="toggleDarkMode()">
</app-sidebar>
  
  <!-- Main Content -->
  <div class="main-content">
    <!-- Mobile Header with Toggle Button -->
    <div class="mobile-header" *ngIf="showNavbar">
      <button class="sidebar-toggle" (click)="toggleSidebar()">
        <i class="fas" [ngClass]="sidebarCollapsed ? 'fa-bars' : 'fa-times'"></i>
      </button>
      <div class="mobile-title">Spor Salonu</div>
      <button class="theme-toggle" (click)="toggleDarkMode()">
        <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
      </button>
    </div>
    
    <!-- Content Area -->
    <main class="content-area">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>

<!-- Security Debug Panel (sadece development'ta görünür) -->
<app-security-debug></app-security-debug>
