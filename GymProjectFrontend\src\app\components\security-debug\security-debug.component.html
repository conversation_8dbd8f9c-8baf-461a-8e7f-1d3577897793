<div class="security-debug-panel" *ngIf="!isProduction">
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">🔒 Güvenlik Debug Paneli</h5>
      <small class="text-muted">Bu panel sadece development modunda görünür</small>
    </div>
    
    <div class="card-body">
      <!-- Güvenlik Durumu -->
      <div class="row mb-4">
        <div class="col-md-6">
          <h6>Güvenlik Durumu</h6>
          <ul class="list-unstyled">
            <li>
              <span class="badge" [ngClass]="securityStats.isActive ? 'badge-success' : 'badge-danger'">
                {{ securityStats.isActive ? 'Aktif' : 'Devre Dışı' }}
              </span>
              Güvenlik Sistemi
            </li>
            <li>
              <span class="badge" [ngClass]="isProduction ? 'badge-warning' : 'badge-info'">
                {{ isProduction ? 'Production' : 'Development' }}
              </span>
              Environment
            </li>
            <li>
              <span class="badge" [ngClass]="securityStats.hasOwnerRole ? 'badge-primary' : 'badge-secondary'">
                {{ securityStats.hasOwnerRole ? 'Owner' : 'Normal User' }}
              </span>
              Kullanıcı Rolü
            </li>
            <li>
              <span class="badge" [ngClass]="securityStats.detectionInterval === 'Active' ? 'badge-success' : 'badge-danger'">
                {{ securityStats.detectionInterval }}
              </span>
              DevTools Detection
            </li>
          </ul>
        </div>
        
        <div class="col-md-6">
          <h6>Kullanıcı Bilgileri</h6>
          <div *ngIf="currentUser; else noUser">
            <p><strong>Email:</strong> {{ currentUser.email }}</p>
            <p><strong>Ad Soyad:</strong> {{ currentUser.nameIdentifier }}</p>
            <p><strong>Roller:</strong> 
              <span class="badge badge-info me-1" *ngFor="let role of currentUser.roles">
                {{ role }}
              </span>
            </p>
          </div>
          <ng-template #noUser>
            <p class="text-muted">Kullanıcı giriş yapmamış</p>
          </ng-template>
        </div>
      </div>

      <!-- Kontrol Butonları -->
      <div class="row mb-4">
        <div class="col-12">
          <h6>Güvenlik Kontrolleri</h6>
          <div class="btn-group me-2 mb-2" role="group">
            <button 
              type="button" 
              class="btn btn-sm"
              [ngClass]="securityStats.isActive ? 'btn-danger' : 'btn-success'"
              (click)="toggleSecurity()">
              {{ securityStats.isActive ? 'Güvenliği Durdur' : 'Güvenliği Başlat' }}
            </button>
            <button 
              type="button" 
              class="btn btn-sm btn-info"
              (click)="testSecurity()">
              Güvenlik Testi
            </button>
            <button 
              type="button" 
              class="btn btn-sm btn-secondary"
              (click)="updateStats()">
              Durumu Yenile
            </button>
          </div>
        </div>
      </div>

      <!-- Test Butonları -->
      <div class="row mb-4">
        <div class="col-12">
          <h6>Güvenlik Testleri</h6>
          <div class="btn-group me-2 mb-2" role="group">
            <button 
              type="button" 
              class="btn btn-sm btn-warning"
              (click)="simulateF12()">
              F12 Simülasyonu
            </button>
            <button 
              type="button" 
              class="btn btn-sm btn-warning"
              (click)="simulateCtrlShiftI()">
              Ctrl+Shift+I Simülasyonu
            </button>
            <button 
              type="button" 
              class="btn btn-sm btn-warning"
              (click)="simulateRightClick()">
              Sağ Tık Simülasyonu
            </button>
            <button 
              type="button" 
              class="btn btn-sm btn-warning"
              (click)="testConsole()">
              Console Test
            </button>
          </div>
        </div>
      </div>

      <!-- Uyarı -->
      <div class="alert alert-warning" role="alert">
        <strong>Dikkat:</strong> Bu panel sadece development modunda görünür. 
        Production build'de otomatik olarak kaldırılır.
      </div>
    </div>
  </div>
</div>
