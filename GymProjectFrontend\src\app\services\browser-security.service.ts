import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { AuthService } from './auth.service';
import { environment } from '../../environments/environment';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class BrowserSecurityService {
  private isBrowser: boolean;
  private isSecurityActive: boolean = false;
  private devToolsDetectionInterval: any;
  private originalConsole: any;
  private securityBypassKey: string = 'gym_security_bypass_' + Date.now();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private authService: AuthService,
    private router: Router
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  /**
   * Güvenlik sistemini başlat
   * Sadece production'da ve owner rolü olmayan kullanıcılar için aktif
   */
  initializeSecurity(): void {
    if (!this.isBrowser) return;

    // Production kontrolü
    if (!environment.production) {
      console.log('[SECURITY] Development modunda güvenlik devre dışı');
      return;
    }

    // Owner rolü kontrolü
    if (this.authService.hasRole('owner')) {
      console.log('[SECURITY] Owner rolü tespit edildi, güvenlik bypass');
      return;
    }

    console.log('[SECURITY] Güvenlik sistemi aktifleştiriliyor...');
    this.isSecurityActive = true;
    
    this.setupConsoleOverride();
    this.setupKeyboardBlocking();
    this.setupContextMenuBlocking();
    this.setupDevToolsDetection();
    this.setupWindowProtection();
    
    console.log('[SECURITY] Güvenlik sistemi aktif');
  }

  /**
   * Güvenlik sistemini durdur
   */
  disableSecurity(): void {
    if (!this.isBrowser || !this.isSecurityActive) return;

    console.log('[SECURITY] Güvenlik sistemi devre dışı bırakılıyor...');
    
    this.isSecurityActive = false;
    this.restoreConsole();
    this.removeEventListeners();
    
    if (this.devToolsDetectionInterval) {
      clearInterval(this.devToolsDetectionInterval);
    }
    
    console.log('[SECURITY] Güvenlik sistemi devre dışı');
  }

  /**
   * Console override - Production'da console.log'ları engelle
   */
  private setupConsoleOverride(): void {
    if (!window.console) return;

    // Orijinal console'u sakla
    this.originalConsole = {
      log: window.console.log,
      warn: window.console.warn,
      error: window.console.error,
      info: window.console.info,
      debug: window.console.debug,
      trace: window.console.trace,
      dir: window.console.dir,
      dirxml: window.console.dirxml,
      table: window.console.table,
      group: window.console.group,
      groupEnd: window.console.groupEnd,
      clear: window.console.clear
    };

    // Console metodlarını override et
    const blockedMessage = () => {
      // Hiçbir şey yapma - sessizce engelle
    };

    window.console.log = blockedMessage;
    window.console.warn = blockedMessage;
    window.console.error = blockedMessage;
    window.console.info = blockedMessage;
    window.console.debug = blockedMessage;
    window.console.trace = blockedMessage;
    window.console.dir = blockedMessage;
    window.console.dirxml = blockedMessage;
    window.console.table = blockedMessage;
    window.console.group = blockedMessage;
    window.console.groupEnd = blockedMessage;
    window.console.clear = blockedMessage;
  }

  /**
   * Console'u geri yükle
   */
  private restoreConsole(): void {
    if (!this.originalConsole) return;

    window.console.log = this.originalConsole.log;
    window.console.warn = this.originalConsole.warn;
    window.console.error = this.originalConsole.error;
    window.console.info = this.originalConsole.info;
    window.console.debug = this.originalConsole.debug;
    window.console.trace = this.originalConsole.trace;
    window.console.dir = this.originalConsole.dir;
    window.console.dirxml = this.originalConsole.dirxml;
    window.console.table = this.originalConsole.table;
    window.console.group = this.originalConsole.group;
    window.console.groupEnd = this.originalConsole.groupEnd;
    window.console.clear = this.originalConsole.clear;
  }

  /**
   * Klavye kısayollarını engelle
   */
  private setupKeyboardBlocking(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
    document.addEventListener('keyup', this.handleKeyUp.bind(this), true);
  }

  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.isSecurityActive) return;

    // F12 tuşu
    if (event.key === 'F12') {
      event.preventDefault();
      event.stopPropagation();
      this.showSecurityWarning('F12 tuşu engellenmiştir.');
      return;
    }

    // Ctrl+Shift+I (Developer Tools)
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
      event.preventDefault();
      event.stopPropagation();
      this.showSecurityWarning('Developer Tools erişimi engellenmiştir.');
      return;
    }

    // Ctrl+Shift+J (Console)
    if (event.ctrlKey && event.shiftKey && event.key === 'J') {
      event.preventDefault();
      event.stopPropagation();
      this.showSecurityWarning('Console erişimi engellenmiştir.');
      return;
    }

    // Ctrl+U (View Source)
    if (event.ctrlKey && event.key === 'u') {
      event.preventDefault();
      event.stopPropagation();
      this.showSecurityWarning('Kaynak kodu görüntüleme engellenmiştir.');
      return;
    }

    // Ctrl+Shift+C (Element Inspector)
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
      event.preventDefault();
      event.stopPropagation();
      this.showSecurityWarning('Element inspector engellenmiştir.');
      return;
    }

    // Ctrl+A (Select All) - Kopyalamayı zorlaştır
    if (event.ctrlKey && event.key === 'a') {
      event.preventDefault();
      event.stopPropagation();
      return;
    }
  }

  private handleKeyUp(event: KeyboardEvent): void {
    if (!this.isSecurityActive) return;
    
    // Aynı tuşlar için keyup'ta da engelle
    if (event.key === 'F12' || 
        (event.ctrlKey && event.shiftKey && (event.key === 'I' || event.key === 'J' || event.key === 'C')) ||
        (event.ctrlKey && event.key === 'u')) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  /**
   * Sağ tık context menu'yu engelle
   */
  private setupContextMenuBlocking(): void {
    document.addEventListener('contextmenu', this.handleContextMenu.bind(this), true);
  }

  private handleContextMenu(event: MouseEvent): void {
    if (!this.isSecurityActive) return;
    
    event.preventDefault();
    event.stopPropagation();
    this.showSecurityWarning('Sağ tık menüsü engellenmiştir.');
  }

  /**
   * Developer Tools detection
   */
  private setupDevToolsDetection(): void {
    // DevTools açık mı kontrol et
    this.devToolsDetectionInterval = setInterval(() => {
      if (!this.isSecurityActive) return;

      if (this.isDevToolsOpen()) {
        this.handleDevToolsDetected();
      }
    }, 500); // Daha sık kontrol et
  }

  private isDevToolsOpen(): boolean {
    // Çoklu detection yöntemi
    return this.detectByWindowSize() ||
           this.detectByConsole() ||
           this.detectByDebugger() ||
           this.detectByPerformance();
  }

  private detectByWindowSize(): boolean {
    const threshold = 160;
    return (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold);
  }

  private detectByConsole(): boolean {
    let devtools = false;
    const element = new Image();
    Object.defineProperty(element, 'id', {
      get: function() {
        devtools = true;
        return 'devtools-detector';
      }
    });

    // Console'a log atmaya çalış
    try {
      console.log(element);
    } catch (e) {
      // Console override edilmişse bile detection çalışsın
    }

    return devtools;
  }

  private detectByDebugger(): boolean {
    // Debugger statement ile detection
    let start = performance.now();
    debugger;
    let end = performance.now();

    // Eğer debugger statement'ta durulduysa, süre uzun olacak
    return (end - start) > 100;
  }

  private detectByPerformance(): boolean {
    // Performance timing ile detection
    const start = performance.now();

    // Dummy işlem
    for (let i = 0; i < 1000; i++) {
      Math.random();
    }

    const end = performance.now();
    const duration = end - start;

    // Normal durumda bu işlem çok hızlı olmalı
    // DevTools açıksa performans düşer
    return duration > 50;
  }

  private handleDevToolsDetected(): void {
    // Güvenlik ihlali logla
    this.logSecurityViolation('DevTools Detection');

    // Uyarı göster
    this.showSecurityWarning('Developer Tools tespit edildi! Güvenlik nedeniyle oturum sonlandırılıyor.');

    // Kullanıcıyı çıkış yaptır
    this.authService.logout();

    // Ana sayfaya yönlendir
    setTimeout(() => {
      window.location.href = '/login';
    }, 1500);
  }

  /**
   * Güvenlik ihlali logla
   */
  private logSecurityViolation(violationType: string): void {
    const violation = {
      type: violationType,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.authService.getCurrentUser()?.userId || 'anonymous'
    };

    // Backend'e güvenlik ihlali raporu gönder (opsiyonel)
    // Bu kısmı backend API'niz varsa aktifleştirebilirsiniz
    console.warn('[SECURITY VIOLATION]', violation);
  }

  /**
   * Window protection
   */
  private setupWindowProtection(): void {
    // Text selection'ı engelle
    document.addEventListener('selectstart', this.preventSelection.bind(this), true);
    document.addEventListener('dragstart', this.preventDrag.bind(this), true);
    
    // Copy/Paste engellemesi
    document.addEventListener('copy', this.preventCopy.bind(this), true);
    document.addEventListener('paste', this.preventPaste.bind(this), true);
  }

  private preventSelection(event: Event): void {
    if (!this.isSecurityActive) return;
    event.preventDefault();
  }

  private preventDrag(event: Event): void {
    if (!this.isSecurityActive) return;
    event.preventDefault();
  }

  private preventCopy(event: Event): void {
    if (!this.isSecurityActive) return;
    event.preventDefault();
  }

  private preventPaste(event: Event): void {
    if (!this.isSecurityActive) return;
    event.preventDefault();
  }

  /**
   * Event listener'ları temizle
   */
  private removeEventListeners(): void {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this), true);
    document.removeEventListener('keyup', this.handleKeyUp.bind(this), true);
    document.removeEventListener('contextmenu', this.handleContextMenu.bind(this), true);
    document.removeEventListener('selectstart', this.preventSelection.bind(this), true);
    document.removeEventListener('dragstart', this.preventDrag.bind(this), true);
    document.removeEventListener('copy', this.preventCopy.bind(this), true);
    document.removeEventListener('paste', this.preventPaste.bind(this), true);
  }

  /**
   * Güvenlik uyarısı göster
   */
  private showSecurityWarning(message: string): void {
    // Daha profesyonel uyarı sistemi
    this.createSecurityModal(message);
  }

  /**
   * Güvenlik modal'ı oluştur
   */
  private createSecurityModal(message: string): void {
    // Mevcut modal'ı temizle
    const existingModal = document.getElementById('security-warning-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Modal container oluştur
    const modal = document.createElement('div');
    modal.id = 'security-warning-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // Modal content oluştur
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
      background: #fff;
      padding: 30px;
      border-radius: 10px;
      max-width: 400px;
      text-align: center;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      animation: modalSlideIn 0.3s ease-out;
    `;

    modalContent.innerHTML = `
      <div style="color: #dc3545; font-size: 48px; margin-bottom: 20px;">🔒</div>
      <h3 style="color: #dc3545; margin: 0 0 15px 0; font-size: 20px;">GÜVENLİK UYARISI</h3>
      <p style="color: #333; margin: 0 0 20px 0; line-height: 1.5;">${message}</p>
      <p style="color: #666; font-size: 14px; margin: 0;">Bu işlem güvenlik nedeniyle engellenmiştir.</p>
    `;

    // CSS animasyon ekle
    if (!document.getElementById('security-modal-styles')) {
      const style = document.createElement('style');
      style.id = 'security-modal-styles';
      style.textContent = `
        @keyframes modalSlideIn {
          from { transform: translateY(-50px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
      `;
      document.head.appendChild(style);
    }

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // 3 saniye sonra modal'ı kapat
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 3000);
  }

  /**
   * Owner bypass kontrolü
   */
  checkOwnerBypass(): boolean {
    return this.authService.hasRole('owner');
  }

  /**
   * Güvenlik durumu kontrolü
   */
  isSecurityEnabled(): boolean {
    return this.isSecurityActive;
  }

  /**
   * Güvenlik istatistikleri
   */
  getSecurityStats(): any {
    return {
      isActive: this.isSecurityActive,
      isProduction: environment.production,
      hasOwnerRole: this.checkOwnerBypass(),
      detectionInterval: this.devToolsDetectionInterval ? 'Active' : 'Inactive'
    };
  }

  /**
   * Manuel güvenlik testi (sadece development'ta)
   */
  testSecurity(): void {
    if (environment.production) {
      console.warn('Security test is not available in production');
      return;
    }

    console.log('=== SECURITY TEST ===');
    console.log('Security Status:', this.getSecurityStats());
    console.log('User Roles:', this.authService.getCurrentUser());
    console.log('Environment:', environment);
  }
}
