# 🔒 Browser Security System

Bu dokümantasyon, GymProject Frontend'inde kurulan browser güvenlik sistemini açıklar.

## 📋 Özellikler

### ✅ Aktif Güvenlik Önlemleri

1. **Developer Tools Engelleme**
   - F12 tuşu engelleme
   - Ctrl+Shift+I (Developer Tools) engelleme
   - Ctrl+Shift+J (Console) engelleme
   - Ctrl+Shift+C (Element Inspector) engelleme
   - Ctrl+U (View Source) engelleme

2. **Context Menu Engelleme**
   - Sağ tık menüsü engelleme
   - "Öğeyi denetle" seçeneği engelleme

3. **Console Override**
   - Production'da tüm console.log, console.warn, console.error mesajları engelleme
   - Console komutları çalıştırma engelleme

4. **DevTools Detection**
   - Window boyut değişikliği ile detection
   - Console kullanım detection
   - Debugger statement detection
   - Performance timing detection

5. **Text Selection & Copy Engelleme**
   - Text seçimi engelleme
   - Kopyalama engelleme
   - Sürükleme engelleme

### 🔓 Owner Bypass Sistemi

- **Owner rolüne sahip kullanıcılar** için tüm güvenlik önlemleri devre dışı
- Development modunda güvenlik sistemi otomatik olarak devre dışı
- Owner kullanıcıları normal şekilde developer tools kullanabilir

## 🚀 Kullanım

### Otomatik Başlatma

Güvenlik sistemi aşağıdaki koşullarda otomatik olarak başlar:

```typescript
// Koşullar:
1. environment.production === true
2. Kullanıcı giriş yapmış olmalı
3. Kullanıcının 'owner' rolü olmamalı
```

### Manuel Kontrol

```typescript
// Güvenlik sistemini manuel başlatma
this.browserSecurityService.initializeSecurity();

// Güvenlik sistemini durdurma
this.browserSecurityService.disableSecurity();

// Güvenlik durumu kontrolü
const isActive = this.browserSecurityService.isSecurityEnabled();

// Owner bypass kontrolü
const isOwner = this.browserSecurityService.checkOwnerBypass();
```

## 🛠️ Development & Testing

### Debug Panel

Development modunda sağ alt köşede bir debug panel görünür:

- **Güvenlik Durumu**: Aktif/Devre Dışı
- **Environment**: Production/Development
- **Kullanıcı Rolü**: Owner/Normal User
- **DevTools Detection**: Active/Inactive

### Test Butonları

Debug panelinde test butonları:

- **F12 Simülasyonu**: F12 tuşuna basılmış gibi davranır
- **Ctrl+Shift+I Simülasyonu**: Developer tools kısayolu simülasyonu
- **Sağ Tık Simülasyonu**: Context menu simülasyonu
- **Console Test**: Console.log test mesajları

### Build Konfigürasyonları

```bash
# Development build (güvenlik devre dışı)
npm run build:dev

# Staging build (güvenlik aktif)
npm run build:staging

# Production build (güvenlik aktif + optimizasyonlar)
npm run build:prod
```

## 📁 Dosya Yapısı

```
src/app/services/
├── browser-security.service.ts          # Ana güvenlik servisi

src/app/components/security-debug/
├── security-debug.component.ts          # Debug paneli component
├── security-debug.component.html        # Debug paneli template
└── security-debug.component.css         # Debug paneli stilleri

src/app/
├── app.component.ts                     # Güvenlik entegrasyonu
└── app.component.html                   # Debug panel ekleme
```

## ⚙️ Konfigürasyon

### Environment Ayarları

```typescript
// environment.production.ts
export const environment = {
  production: true,  // Güvenlik aktif
  // ...
};

// environment.development.ts
export const environment = {
  production: false, // Güvenlik devre dışı
  // ...
};
```

### Angular Build Ayarları

```json
// angular.json - Production konfigürasyonu
"production": {
  "optimization": true,
  "sourceMap": false,        // Source map'leri kapat
  "extractLicenses": true,
  "namedChunks": false,
  "aot": true,
  "buildOptimizer": true
}
```

## 🔍 Güvenlik Seviyeleri

### Seviye 1: Keyboard Shortcuts
- F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+Shift+C, Ctrl+U engelleme

### Seviye 2: Context Menu
- Sağ tık menüsü tamamen engelleme

### Seviye 3: Console Override
- Tüm console metodlarını sessizce engelleme

### Seviye 4: DevTools Detection
- Çoklu yöntemle developer tools açılma tespiti
- Tespit edildiğinde kullanıcıyı çıkış yaptırma

### Seviye 5: Content Protection
- Text seçimi, kopyalama, sürükleme engelleme

## ⚠️ Önemli Notlar

### Güvenlik Sınırlamaları

1. **Client-side güvenlik %100 güvenli değildir**
   - Deneyimli kullanıcılar bu önlemleri bypass edebilir
   - Asıl güvenlik backend'te olmalıdır

2. **Kullanıcı Deneyimi**
   - Normal kullanıcılar için bazı işlevler kısıtlanabilir
   - Owner rolü bu kısıtlamalardan muaftır

3. **Browser Uyumluluğu**
   - Modern browser'larda test edilmiştir
   - Eski browser'larda bazı özellikler çalışmayabilir

### Best Practices

1. **Backend Güvenlik**: Her zaman backend'te authorization kontrolü yapın
2. **Rate Limiting**: API endpoint'lerinde rate limiting kullanın
3. **Input Validation**: Tüm kullanıcı girdilerini validate edin
4. **HTTPS**: Production'da her zaman HTTPS kullanın

## 🧪 Test Senaryoları

### Owner Kullanıcı Testi
1. Owner rolüyle giriş yapın
2. F12'ye basın → Developer tools açılmalı
3. Console'da komut çalıştırın → Çalışmalı

### Normal Kullanıcı Testi
1. Admin/Member rolüyle giriş yapın
2. F12'ye basın → Güvenlik uyarısı görünmeli
3. Sağ tık yapın → Context menu engellenmeli
4. Console'da komut çalıştırın → Çalışmamalı

### Production Build Testi
1. `npm run build:prod` ile build alın
2. Production server'da deploy edin
3. Normal kullanıcıyla test edin
4. Source map'lerin olmadığını kontrol edin

## 📞 Destek

Güvenlik sistemi ile ilgili sorunlar için:
1. Debug panelini kontrol edin
2. Browser console'da hata mesajlarını inceleyin
3. Environment ve kullanıcı rolü ayarlarını doğrulayın
